import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const Hero = () => {
  return (
    <section className="min-h-screen flex items-center justify-center px-6 py-20 relative">
      <div className="max-w-5xl mx-auto relative z-10">
        <div className="text-center mb-16">
          <div className="mb-8 mt-8 md:mt-0">
            <span className="inline-block px-4 py-2 bg-white/80 backdrop-blur-sm border border-gray-200 text-gray-700 text-sm font-medium rounded-full mb-8 shadow-sm">
              Available for new projects
            </span>
          </div>
          
          <h1 className="text-6xl md:text-8xl font-semibold text-gray-900 mb-8 leading-[0.9] tracking-tight">
            Design that grows
            <br />
            <span className="bg-gradient-to-b from-gray-400 to-gray-600 bg-clip-text text-transparent">your business</span>
          </h1>
          
          <p className="text-xl md:text-2xl text-gray-600 mb-12 font-light max-w-3xl mx-auto leading-relaxed">
            I create thoughtful brands, websites and apps that resonate with your customers and drive real business results.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16">
            <Button 
              size="lg" 
              className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-6 text-lg rounded-full transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl"
              asChild
            >
              <Link to="/work">
                View my work
                <ArrowRight className="w-5 h-5" />
              </Link>
            </Button>
            <Button 
              variant="outline"
              size="lg" 
              className="border-gray-300 hover:border-gray-400 text-gray-700 px-8 py-6 text-lg rounded-full transition-all duration-300 bg-white/80 backdrop-blur-sm hover:bg-white/90 shadow-sm hover:shadow-md"
              asChild
            >
              <Link to="/contact">Get in touch</Link>
            </Button>
          </div>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8 text-center">
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/40 shadow-sm">
            <div className="text-4xl font-black text-gray-900 mb-2">250+</div>
            <p className="text-gray-600">Projects completed</p>
          </div>
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/40 shadow-sm">
            <div className="text-4xl font-black text-gray-900 mb-2">25+</div>
            <p className="text-gray-600">Years experience</p>
          </div>
          <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-white/40 shadow-sm">
            <div className="text-4xl font-black text-gray-900 mb-2">$1B+</div>
            <p className="text-gray-600">Capital raised</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
